/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/button-has-type */
/* eslint-disable no-lonely-if */
/**
 * 登录应用主组件
 * 管理登录流程和不同登录组件的切换
 */

import React, { useState, useCallback } from 'react';
import { LoginForm } from './LoginForm';
import { LoadingSpinner } from './LoadingSpinner';
import { LoginManager } from '../services/LoginManager';

interface LoginAppProps {
  loginManager: LoginManager;
  onLoginSuccess: () => void;
}

export type LoginStep = 'form' | 'captcha' | 'phone' | 'loading';

export const LoginApp: React.FC<LoginAppProps> = ({
  loginManager,
  onLoginSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState<LoginStep>('form');
  const [loginData, setLoginData] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const handleFormSubmit = useCallback(
    async (formData: any) => {
      setError('');
      setCurrentStep('loading');
      setLoginData(formData);

      try {
        const result = await loginManager.performLogin(
          formData.username,
          formData.password
        );

        if (result.success) {
          // 登录成功
          if (formData.rememberMe) {
            localStorage.setItem('rememberMe', 'true');
          }

          // 保存用户信息
          loginManager.saveUserSession(result.token, formData.username);

          // 调用成功回调
          onLoginSuccess();
        } else {
          // 根据错误类型决定下一步
          if (result.needCaptcha) {
            setCurrentStep('captcha');
          } else if (result.needPhoneVerify) {
            setCurrentStep('phone');
          } else {
            setError(result.message || '登录失败');
            setCurrentStep('form');
          }
        }
      } catch (error) {
        console.error('Login error:', error);
        setError('登录失败，请稍后重试');
        setCurrentStep('form');
      }
    },
    [loginManager, onLoginSuccess]
  );

  const handleCaptchaSuccess = useCallback(
    async (captchaData: any) => {
      // 处理极验验证成功
      setCurrentStep('loading');

      try {
        const result = await loginManager.performLoginWithCaptcha(
          loginData,
          captchaData
        );

        if (result.success) {
          loginManager.saveUserSession(result.token, loginData.username);
          onLoginSuccess();
        } else if (result.needPhoneVerify) {
          setCurrentStep('phone');
        } else {
          setError(result.message || '验证失败');
          setCurrentStep('form');
        }
      } catch (error) {
        setError('验证失败，请重试');
        setCurrentStep('form');
      }
    },
    [loginManager, loginData, onLoginSuccess]
  );

  const handlePhoneVerifySuccess = useCallback(
    async (phoneData: any) => {
      // 处理手机验证成功
      setCurrentStep('loading');

      try {
        const result = await loginManager.performLoginWithPhone(
          loginData,
          phoneData
        );

        if (result.success) {
          loginManager.saveUserSession(result.token, loginData.username);
          onLoginSuccess();
        } else {
          setError(result.message || '手机验证失败');
          setCurrentStep('form');
        }
      } catch (error) {
        setError('手机验证失败，请重试');
        setCurrentStep('form');
      }
    },
    [loginManager, loginData, onLoginSuccess]
  );

  const handleBackToForm = useCallback(() => {
    setCurrentStep('form');
    setError('');
  }, []);

  const handleCloseWindow = useCallback(() => {
    // 关闭窗口
    if ((window as any).electronAPI?.ipcSend) {
      (window as any).electronAPI.ipcSend('close-window');
    } else {
      window.close();
    }
  }, []);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'form':
        return (
          <LoginForm
            onSubmit={handleFormSubmit}
            error={error}
            isLoading={false}
          />
        );

      case 'captcha':
        return (
          <div className="captcha-container">
            <h3>请完成安全验证</h3>
            <p>为了您的账户安全，请完成以下验证</p>
            {/* 极验组件将在这里渲染 */}
            <div id="captcha-container" className="captcha-widget"></div>
            <button className="back-btn" onClick={handleBackToForm}>
              返回登录
            </button>
          </div>
        );

      case 'phone':
        return (
          <div className="phone-verify-container">
            <h3>手机号验证</h3>
            <p>请输入您的手机号码进行身份验证</p>
            {/* 手机验证组件将在这里渲染 */}
            <div id="phone-verify-container" className="phone-widget"></div>
            <button className="back-btn" onClick={handleBackToForm}>
              返回登录
            </button>
          </div>
        );

      case 'loading':
        return <LoadingSpinner message="正在登录..." />;

      default:
        return null;
    }
  };

  return (
    <div className="design-login-app">
      <div className="design-login-container">
        {/* 右上角关闭按钮 */}
        <button
          className="close-button"
          type="button"
          onClick={handleCloseWindow}
          title="关闭"
        >
          ✕
        </button>

        <div className="login-content">{renderCurrentStep()}</div>
      </div>
    </div>
  );
};
