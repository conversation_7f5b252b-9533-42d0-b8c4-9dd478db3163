// import { message } from '@ht/sprite-ui';
import geetestApi from '@/services/geetest';
import { getIsHyperProduct } from './utils';

declare global {
  interface Window {
    geetestState: string;
    initGeetest: (config: any, callBack: any) => void;
  }
}

interface IInitGtResponse {
  retCode: number;
  message: string;
  value: any; // 成功的时候，返回的 challenge 流水号，（并不是接口返回的，是极验插件获取的）其他时候为空，
}

export default class GeeTest {
  private readonly api: typeof geetestApi;

  // cid: string; // 业务系统唯一标识，不会变化

  // offline: boolean;

  // offlineChallenge: string;

  // gtInstance: any; //  极验的验证实例,可以复用，实际不断变化的是challenge，十分钟更新一次，sdk内部机制自动触发reset事件更新challenge

  constructor() {
    this.api = geetestApi;
  }

  initGt: <T = IInitGtResponse>(arg?: T) => Promise<T> = () => {
    //
    const flag = getIsHyperProduct();
    if (window?.geetestState === 'loading') {
      return new Promise((resolve, reject) => {});
    }
    window.geetestState = 'loading';
    return new Promise((resolve, reject) => {
      this.api
        .getGeeTestConfigParam()
        .then((res) => {
          const config = {
            static_servers: res.value.static_servers,
            slide: res.value.slide,
            challenge: res.value.challenge,
            api_server: res.value.api_server,
            type: res.value.type,
            fullpage: res.value.fullpage,
            click: res.value.click,
            cid: res.value.cid, // 该字段涉及隐私，安全上不应该使用

            product: 'bind', // 设置下一步验证的展现形式。 popup（弹出式）   float（浮动式）custom（与popup类似，但是可以自定义弹出区域）   bind（隐藏按钮类型）
            width: '100%', // 设置按钮的长度。默认：300px * 44px。注意只能调节宽度，高度固定不变。bind模式下该按钮不可见，可以不设定
            https: flag, // 设置验证使用https请求。
            pure: true, // 设置验证码的loading是否显示。bind模式下不可见，可以不设定
            lang: 'zh-cn', // 设置验证界面文字的语言  zh-cn（简体中文）
            time: 30000, // 设置验证过程中单个请求超时时间
          };

          // 【第二步】，解析接口返回参数，data（data中必须的配置参数应该包括：cid, data.challenge）
          // 参数说明参照http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=87292785#id-11.5.1Web%E5%89%8D%E7%AB%AF%E6%8E%A5%E5%85%A5-%E9%9A%90%E8%97%8F%E6%8C%89%E9%92%AE%E5%BC%8F%E8%B0%83%E7%94%A8%E7%A4%BA%E4%BE%8B%EF%BC%9A
          window.initGeetest(config, (catchObj: any) => {
            resolve(this.initGeetestBindHandler(catchObj));
          });
        })
        .catch((res) => {
          window.geetestState = 'failed';
          reject(res);
          //   this.$message.error(res.msg);
        });
    });
  };

  private readonly initGeetestBindHandler: (gtInstance: any) => Promise<any> = (
    gtInstance: any,
  ) => {
    return new Promise((resolve, reject) => {
      gtInstance
        .onReady(() => {
          // 表示极验已经准备好了，可以调用verify唤起 行为验证图形方法
          // 当product为bind类型时，可以调用该接口进行验证
          gtInstance.verify(); // product根据bind方法调用
        })
        .onSuccess(() => {
          // 获取用户进行成功验证(onSuccess)所得到的结果，该结果用于进行服务端 SDK 进行二次验证。getValidate 方法返回一个对象，
          // 该对象包含 geetest_challenge，geetest_validate，geetest_seccode 字段
          const validateData = gtInstance.getValidate();
          const { geetest_challenge } = validateData;
          window.geetestState = 'succeed';
          resolve({
            retCode: 0,
            message: '极验成功，返回流水号',
            value: geetest_challenge,
          });
          // 调用后台验证接口
          // this.api
          //   .verifyGeetestRespCode({
          //     challenge: geetest_challenge,
          //   })
          //   .then((result) => {
          //     if (result.retCode === 0) {
          //       // 验证成功的时候，把流水号返回，因为这个流水号，个人觉得不能存到cookie等，所以直接返回，在组件内部调用的时候，存一下useState即可
          //       result.value = geetest_challenge;
          //       resolve(result);
          //     } else {
          //       reject(result);
          //     }
          //   });
        })
        .onError((error: any) => {
          //   console.log("gt onError");
          window.geetestState = 'failed';
          resolve({
            retCode: 99,
            message: 'gt插件验证失败，请重试',
            value: '',
          });
          gtInstance.destroy();
        })
        .onClose(() => {
          window.geetestState = 'ready';
          // 用户手动关闭
          resolve({
            retCode: 100,
            message: '用户手动关闭行为验证',
            value: '',
          });
        });
    });
  };
}
