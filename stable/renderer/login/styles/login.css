/**
 * 登录模块样式文件
 */

/* 登录应用容器 */
.login-app {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
}

.login-container {
  background: rgba(255, 255, 255, 95%);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 10%);
  padding: 40px;
  width: 100%;
  max-width: 380px;
  backdrop-filter: blur(10px);
  margin: 0 auto;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 登录内容区域 */
.login-content {
  margin-bottom: 20px;
}

/* 登录表单 */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}

.form-group input[type="text"],
.form-group input[type="password"] {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
  box-sizing: border-box;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 10%);
}

.form-group input.error {
  border-color: #e74c3c;
}

.error-text {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 复选框组 */
.checkbox-group {
  margin-bottom: 24px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.checkbox-text {
  user-select: none;
}

/* 错误消息 */
.error-message {
  background: #ffeaea;
  color: #e74c3c;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  background: #667eea;
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.1s;
}

.login-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* 表单链接 */
.form-links {
  text-align: center;
  margin-top: 20px;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.forgot-password:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.spinner {
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
}

.spinner-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #667eea;
  animation: spinner-bounce 1.4s ease-in-out infinite both;
}

.spinner-circle:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-circle:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes spinner-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-message {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 尺寸变体 */
.loading-spinner.small .spinner-circle {
  width: 6px;
  height: 6px;
}

.loading-spinner.large .spinner-circle {
  width: 12px;
  height: 12px;
}

/* 验证容器 */
.captcha-container,
.phone-verify-container {
  text-align: center;
  padding: 20px;
}

.captcha-container h3,
.phone-verify-container h3 {
  color: #333;
  margin-bottom: 8px;
}

.captcha-container p,
.phone-verify-container p {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.captcha-widget,
.phone-widget {
  margin: 20px 0;
  min-height: 100px;
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.back-btn {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.back-btn:hover {
  background: #667eea;
  color: white;
}

/* 登录底部 */
.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.login-footer p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

/* 手机验证组件样式 */
.verify-code-input {
  display: flex;
  gap: 8px;
}

.verify-code-input input {
  flex: 1;
}

.send-code-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.3s;
}

.send-code-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.send-code-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.phone-verify-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.verify-btn {
  flex: 1;
  background: #667eea;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.verify-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.verify-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 根据设计稿实现的现代登录表单样式 */
.modern-login-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* 网络异常提示 */
.network-error-alert {
  display: flex;
  align-items: center;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  color: #dc2626;
  font-size: 14px;
}

.network-error-alert .error-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #dc2626;
}

.network-error-alert .error-text {
  flex: 1;
}

/* 输入框组 */
.input-group {
  margin-bottom: 20px;
}

.modern-input {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  color: #374151;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.modern-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 10%);
}

.modern-input.error {
  border-color: #dc2626;
}

.modern-input::placeholder {
  color: #9ca3af;
}

/* 密码输入框包装器 */
.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  padding-right: 48px;
}

.password-toggle-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: color 0.2s ease;
}

.password-toggle-btn:hover {
  color: #374151;
}

.password-toggle-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.eye-icon {
  font-size: 18px;
  user-select: none;
}

/* 字段错误提示 */
.field-error {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 4px;
}

/* 现代登录按钮 */
.modern-login-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 30%);
}

.modern-login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.modern-login-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 自动登录组 */
.auto-login-group {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.auto-login-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
  color: #374151;
}

.auto-login-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 3px;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.auto-login-label:hover .checkmark {
  border-color: #3b82f6;
}

.auto-login-checkbox:checked + .checkmark {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.auto-login-checkbox:checked + .checkmark::after {
  content: "";
  position: absolute;
  display: block;
  left: 5px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.auto-login-text {
  font-size: 14px;
  color: #374151;
}

/* 登录应用容器重写 - 更简洁的设计 */
.login-app {
  height: 100vh;
  background: #f8fafc;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  padding: 20px;
  box-sizing: border-box;
}

.login-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 10%);
  padding: 48px 40px;
  width: 100%;
  max-width: 440px;
  position: relative;
}

/* 二维码区域占位 - 左上角 */
.login-container::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 8px;
  opacity: 0.6;
}

/* 关闭按钮占位 - 右上角 */
.login-container::after {
  content: "✕";
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  cursor: pointer;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    margin: 20px;
    padding: 30px 20px;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .verify-code-input {
    flex-direction: column;
  }

  .phone-verify-actions {
    flex-direction: column;
  }

  .modern-login-form {
    max-width: 100%;
  }

  .login-container::before {
    width: 60px;
    height: 60px;
  }
}
